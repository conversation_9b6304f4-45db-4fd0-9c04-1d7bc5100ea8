serena, hãy active dự án, sau đó tạo initial instructions, tiếp theo thực hiện onboarding dự án.

Tiếp tục hãy làm các task sau:

- Tạo 1 tool scraping dữ liệu từ website https://bonbanh.com/, vào trang chủ và lấy danh sách các hãng xe và model xe, sau đó lưu vào dữ liệu 2 bảng riêng, có các thông tin cơ bản (id, name, slug, url)
- Với mỗi 1 model xe sẽ có 1 tool riêng để truy cập vào url của model đó, lấy danh sách các đời xe (năm) mà có xe đang rao bán. Sau đó lại vào link từng năm (ví dụ: https://bonbanh.com/oto/ford-ranger-nam-2023) để lấy danh sách các phiên bản xe.
Với mỗi 1 phiên bản xe sẽ lưu lại vào database bảng car_version_year gồm các thông tin (brand, model, version, year, url)
- Tạo 1 file riêng để tạo database lần đầu

Yêu cầu kỹ thuật: 
- Với mỗi lần tạo tool, hãy tự truy cập vào link website để hiểu cấu trúc HTML trước.
- danh sách link các năm của model xe ở trong box html "{te}
- Sử dụng python.
- Phân tích HTML bằng Xpath
- Lưu dữ liệu vào database mysql (thông tin kết nối trong file .env)